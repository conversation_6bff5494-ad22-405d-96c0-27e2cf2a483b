Line 1: This is a large test file to demonstrate the max_read_lines functionality
Line 2: Each line contains some meaningful content for testing purposes
Line 3: The file will have many lines to test the truncation feature
Line 4: When reading this file, only a limited number of lines should be shown
Line 5: After the limit, a summary should indicate remaining lines
Line 6: This helps prevent overwhelming the context with very large files
Line 7: The configuration allows users to adjust the maximum lines read
Line 8: Default setting is 1000 lines, but can be configured from 100 to 10000
Line 9: This is particularly useful for large log files or data files
Line 10: The start_line parameter still works with the max_read_lines limit
Line 11: You can start reading from any line and still respect the limit
Line 12: For example, start from line 500 and read max 100 lines
Line 13: This gives you lines 500-599 if the file is long enough
Line 14: The summary will show "Read lines 500-599, X lines left"
Line 15: This makes it easy to navigate through large files systematically
Line 16: The feature is implemented in the extractFileContent function
Line 17: It uses the applyLineFiltering helper function internally
Line 18: The configuration is read from qax-codegen.readFile.maxReadLines
Line 19: This setting is stored in the VSCode workspace configuration
Line 20: Users can modify it through VSCode settings or settings.json
Line 21: The implementation handles edge cases like files shorter than the limit
Line 22: It also validates that start_line doesn't exceed the file length
Line 23: Error messages include the actual file line count for reference
Line 24: The feature works with all supported file types (text, PDF, DOCX)
Line 25: Binary files are still handled according to existing logic
Line 26: The line counting and filtering happens after text extraction
Line 27: This ensures consistent behavior across different file formats
Line 28: The summary format is: [Read lines X-Y, Z lines left]
Line 29: This provides clear information about what was read and what remains
Line 30: The feature helps maintain reasonable context window usage
