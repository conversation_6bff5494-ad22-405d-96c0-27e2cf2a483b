import * as path from "path"
import fs from "fs/promises"
import { Anthropic } from "@anthropic-ai/sdk"
import { callTextExtractionFunctions } from "./extract-text"
import { extractImageContent } from "./extract-images"

export type FileContentResult = {
	text: string
	imageBlock?: Anthropic.ImageBlockParam
}

/**
 * Apply line filtering to text content based on start line number and max read lines
 */
function applyLineFiltering(text: string, startLine?: number, maxReadLines?: number): string {
	const lines = text.split('\n')
	const totalLines = lines.length

	// Validate start line
	if (startLine && startLine > totalLines) {
		throw new Error(`Start line ${startLine} exceeds file length of ${totalLines} lines`)
	}

	// Determine the actual start line (1-based to 0-based conversion)
	const actualStartLine = startLine && startLine > 1 ? startLine - 1 : 0

	// If no maxReadLines specified, return from start line to end
	if (!maxReadLines) {
		return lines.slice(actualStartLine).join('\n')
	}

	// Calculate end line based on maxReadLines
	const endLine = actualStartLine + maxReadLines
	const selectedLines = lines.slice(actualStartLine, endLine)

	// Add summary if there are more lines
	let result = selectedLines.join('\n')
	if (endLine < totalLines) {
		const readStart = actualStartLine + 1 // Convert back to 1-based for display
		const readEnd = Math.min(endLine, totalLines) // Actual last line read (1-based)
		const remainingLines = totalLines - endLine
		result += `\n\n[Read lines ${readStart}-${readEnd}, ${remainingLines} lines left]`
	}

	return result
}

/**
 * Extract content from a file, handling both text and images
 * Extra logic for handling images based on whether the model supports images
 */
export async function extractFileContent(absolutePath: string, modelSupportsImages: boolean, startLine?: number, maxReadLines?: number): Promise<FileContentResult> {
	// Check if file exists first
	try {
		await fs.access(absolutePath)
	} catch (error) {
		throw new Error(`File not found: ${absolutePath}`)
	}

	const fileExtension = path.extname(absolutePath).toLowerCase()
	const imageExtensions = [".png", ".jpg", ".jpeg", ".webp"]
	const isImage = imageExtensions.includes(fileExtension)

	if (isImage && modelSupportsImages) {
		const imageResult = await extractImageContent(absolutePath)

		if (imageResult.success) {
			return {
				text: "Successfully read image",
				imageBlock: imageResult.imageBlock,
			}
		} else {
			throw new Error(imageResult.error)
		}
	} else if (isImage && !modelSupportsImages) {
		throw new Error(`Current model does not support image input`)
	} else {
		// Handle text files using existing extraction functions
		try {
			const textContent = await callTextExtractionFunctions(absolutePath)
			const filteredContent = applyLineFiltering(textContent, startLine, maxReadLines)
			return {
				text: filteredContent,
			}
		} catch (error) {
			const errorMessage = error instanceof Error ? error.message : "Unknown error"
			throw new Error(`Error reading file: ${errorMessage}`)
		}
	}
}
