// Test script to verify the max_read_lines functionality
const fs = require('fs');
const path = require('path');

// Mock the extractFileContent function for testing
async function testApplyLineFiltering() {
    console.log('Testing applyLineFiltering function...');
    
    // Create test content
    const testContent = Array.from({length: 20}, (_, i) => `Line ${i + 1}: This is line ${i + 1}`).join('\n');
    
    // Test function (copied from extract-file-content.ts)
    function applyLineFiltering(text, startLine, maxReadLines) {
        const lines = text.split('\n');
        const totalLines = lines.length;

        // Validate start line
        if (startLine && startLine > totalLines) {
            throw new Error(`Start line ${startLine} exceeds file length of ${totalLines} lines`);
        }

        // Determine the actual start line (1-based to 0-based conversion)
        const actualStartLine = startLine && startLine > 1 ? startLine - 1 : 0;
        
        // If no maxReadLines specified, return from start line to end
        if (!maxReadLines) {
            return lines.slice(actualStartLine).join('\n');
        }

        // Calculate end line based on maxReadLines
        const endLine = actualStartLine + maxReadLines;
        const selectedLines = lines.slice(actualStartLine, endLine);
        
        // Add summary if there are more lines
        let result = selectedLines.join('\n');
        if (endLine < totalLines) {
            const readStart = actualStartLine + 1; // Convert back to 1-based for display
            const readEnd = endLine; // This is already the correct end line number
            const remainingLines = totalLines - endLine;
            result += `\n\n[Read lines ${readStart}-${readEnd}, ${remainingLines} lines left]`;
        }
        
        return result;
    }
    
    console.log('\n=== Test 1: Read first 5 lines ===');
    const result1 = applyLineFiltering(testContent, undefined, 5);
    console.log(result1);
    
    console.log('\n=== Test 2: Read lines 6-10 (start_line=6, max_read_lines=5) ===');
    const result2 = applyLineFiltering(testContent, 6, 5);
    console.log(result2);
    
    console.log('\n=== Test 3: Read from line 15 with max 10 lines (should read to end) ===');
    const result3 = applyLineFiltering(testContent, 15, 10);
    console.log(result3);
    
    console.log('\n=== Test 4: Read all lines (no max_read_lines) ===');
    const result4 = applyLineFiltering(testContent, undefined, undefined);
    console.log('Total lines returned:', result4.split('\n').length);
    
    console.log('\n=== Test 5: Error case - start_line beyond file length ===');
    try {
        const result5 = applyLineFiltering(testContent, 25, 5);
        console.log('ERROR: Should have thrown an error');
    } catch (error) {
        console.log('Expected error:', error.message);
    }
    
    console.log('\nAll tests completed!');
}

// Run the test
if (require.main === module) {
    testApplyLineFiltering().catch(console.error);
}

module.exports = { testApplyLineFiltering };
