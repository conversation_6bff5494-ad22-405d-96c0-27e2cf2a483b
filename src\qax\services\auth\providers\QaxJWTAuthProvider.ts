// 复用 Cline 的 state.ts 进行安全存储
import { getSecret, storeSecret } from "@/core/storage/state"
import { ExtensionContext } from "vscode"
import type { QaxUserInfo } from "@shared/QaxUserInfo"
import { validateJWTToken } from "@/qax/utils/jwt"

/**
 * JWT token 的原始 payload 结构（用于解析）
 * 使用下划线命名，与 JWT 标准保持一致
 */
interface JWTPayload {
	display_name: string
	name: string
	email: string
	employee_number: string
	sub: string
	iss: string
	aud: string[]
	exp: number
	iat: number
}

/**
 * Qax JWT Authentication Provider
 * Handles JWT token-based authentication for Qax services
 */
export class QaxJWTAuthProvider {
	private _config: any
	private _currentToken: string | null = null
	private _currentUser: QaxUserInfo | null = null

	constructor(config: any) {
		this._config = config || {}
	}

	get config(): any {
		return this._config
	}

	set config(value: any) {
		this._config = value
	}

	/**
	 * Gets the current JWT authentication token
	 */
	async getAuthToken(): Promise<string | null> {
		return this._currentToken
	}

	/**
	 * Parses and validates a JWT token
	 * @param token - The JWT token to parse
	 * @returns Parsed and converted user info or null if invalid
	 */
	private parseJWTToken(token: string): QaxUserInfo | null {
		try {
			console.log("[QAX JWT] Parsing token, length:", token?.length, "preview:", token?.substring(0, 50) + "...")

			// 使用 jwt.ts 中的工具函数进行验证
			validateJWTToken(token)

			// 解析 JWT payload
			const parts = token.split(".")
			console.log("[QAX JWT] Token parts count:", parts.length)

			const decodedPayload = Buffer.from(parts[1], "base64url").toString("utf-8")
			console.log("[QAX JWT] Decoded payload length:", decodedPayload.length)

			const parsedPayload = JSON.parse(decodedPayload) as JWTPayload
			console.log("[QAX JWT] Parsed payload fields:", Object.keys(parsedPayload))

			// Basic validation
			if (!parsedPayload.sub || !parsedPayload.email) {
				console.error("[QAX JWT] Missing required fields - sub:", !!parsedPayload.sub, "email:", !!parsedPayload.email)
				throw new Error("Invalid JWT payload: missing required fields")
			}

			console.log("[QAX JWT] Successfully parsed token for user:", parsedPayload.email)

			// Convert to QaxUserInfo format (camelCase naming)
			return {
				sub: parsedPayload.sub,
				displayName: parsedPayload.display_name,
				name: parsedPayload.name,
				email: parsedPayload.email,
				employeeNumber: parsedPayload.employee_number,
				iss: parsedPayload.iss,
				aud: parsedPayload.aud,
				exp: parsedPayload.exp,
				iat: parsedPayload.iat,
			}
		} catch (error) {
			console.error("[QAX JWT] Failed to parse JWT token:", error)
			console.error("[QAX JWT] Token preview:", token?.substring(0, 100) + "...")
			return null
		}
	}

	/**
	 * Signs in with a JWT token
	 * @param context - VSCode extension context
	 * @param token - The JWT token
	 * @returns Promise resolving to user data
	 */
	async signIn(context: ExtensionContext, token: string): Promise<QaxUserInfo> {
		try {
			const payload = this.parseJWTToken(token)
			if (!payload) {
				throw new Error("Invalid JWT token")
			}

			// Store the token securely
			await this.storeAuthToken(context, token)

			this._currentToken = token
			this._currentUser = payload

			return payload
		} catch (error) {
			console.error("Failed to sign in with JWT token:", error)
			throw error
		}
	}

	/**
	 * Signs out the current user
	 * @param context - VSCode extension context (optional, for clearing stored token)
	 */
	async signOut(context?: ExtensionContext): Promise<void> {
		try {
			this._currentToken = null
			this._currentUser = null

			// Clear the stored token if context is provided
			if (context) {
				await storeSecret(context, "qaxAccountToken", undefined)
			}
			console.log("QAX user signed out successfully.")
		} catch (error) {
			console.error("Failed to sign out from QAX:", error)
			throw error
		}
	}

	/**
	 * Stores the JWT token securely
	 * @param context - VSCode extension context
	 * @param token - The JWT token to store
	 */
	private async storeAuthToken(context: ExtensionContext, token: string): Promise<void> {
		try {
			await storeSecret(context, "qaxAccountToken", token)
		} catch (error) {
			console.error("Failed to store QAX JWT token:", error)
			throw error
		}
	}

	/**
	 * Restores the JWT token from storage
	 * @param context - VSCode extension context
	 * @returns Promise resolving to user data or null
	 */
	async restoreAuthCredential(context: ExtensionContext): Promise<QaxUserInfo | null> {
		try {
			const token = await getSecret(context, "qaxAccountToken")
			if (!token) {
				console.log("No stored QAX JWT token found.")
				return null
			}

			const payload = this.parseJWTToken(token)
			if (!payload) {
				console.error("Stored QAX JWT token is invalid or expired.")
				// Clear invalid token
				await storeSecret(context, "qaxAccountToken", undefined)
				return null
			}

			this._currentToken = token
			this._currentUser = payload

			return payload
		} catch (error) {
			console.error("Failed to restore QAX JWT token:", error)
			return null
		}
	}

	/**
	 * Refreshes the authentication token (for JWT, this would typically involve
	 * getting a new token from the auth server, but for now we'll just validate the current one)
	 */
	async refreshAuthToken(): Promise<string | null> {
		if (!this._currentToken) {
			return null
		}

		// For JWT tokens, we typically don't refresh them client-side
		// Instead, we would need to redirect to the auth server for a new token
		// For now, just return the current token if it's still valid
		const payload = this.parseJWTToken(this._currentToken)
		if (payload) {
			return this._currentToken
		}

		// Token is invalid/expired, clear it
		this._currentToken = null
		this._currentUser = null
		return null
	}

	/**
	 * Gets the current user data
	 */
	getCurrentUser(): QaxUserInfo | null {
		return this._currentUser
	}
}
