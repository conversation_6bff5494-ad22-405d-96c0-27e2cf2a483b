// Simple test to verify the start_line functionality
const fs = require('fs');
const path = require('path');

// Import the function we want to test
const { extractFileContent } = require('./src/integrations/misc/extract-file-content');

async function testStartLineFunctionality() {
    console.log('Testing start_line functionality...');
    
    // Create a test file
    const testFilePath = path.join(__dirname, 'test_file.txt');
    const testContent = `Line 1: First line
Line 2: Second line
Line 3: Third line
Line 4: Fourth line
Line 5: Fifth line
Line 6: Sixth line
Line 7: Seventh line
Line 8: Eighth line
Line 9: Ninth line
Line 10: Tenth line`;
    
    fs.writeFileSync(testFilePath, testContent);
    
    try {
        // Test 1: Read entire file (no start_line)
        console.log('\nTest 1: Reading entire file');
        const result1 = await extractFileContent(testFilePath, false);
        console.log('Result:', result1.text.split('\n').length, 'lines');
        
        // Test 2: Read from line 5
        console.log('\nTest 2: Reading from line 5');
        const result2 = await extractFileContent(testFilePath, false, 5);
        console.log('Result:', result2.text);
        
        // Test 3: Read from line 1 (should be same as no start_line)
        console.log('\nTest 3: Reading from line 1');
        const result3 = await extractFileContent(testFilePath, false, 1);
        console.log('Result lines:', result3.text.split('\n').length);
        
        // Test 4: Try to read from line beyond file length
        console.log('\nTest 4: Reading from line 15 (should error)');
        try {
            const result4 = await extractFileContent(testFilePath, false, 15);
            console.log('ERROR: Should have thrown an error');
        } catch (error) {
            console.log('Expected error:', error.message);
        }
        
    } finally {
        // Clean up
        fs.unlinkSync(testFilePath);
    }
    
    console.log('\nAll tests completed!');
}

// Run the test if this file is executed directly
if (require.main === module) {
    testStartLineFunctionality().catch(console.error);
}

module.exports = { testStartLineFunctionality };
